import * as actions from './action'
import { CommonActionTypes } from '../services/types'
import { combineReducers } from 'redux'
import supportTicketReducer from './supportTicketReducer'

//@ts-ignore
export const initState: any = {
    isDoneSetProps: false
}

export interface ICommonState {
    isDoneSetProps: boolean
    navigation?: any
    Toast: any
    typeCode: string
    name: string
    profile: any
    VideoPlayer: any
}

const commonReducer = (
    state: ICommonState = initState,
    action: CommonActionTypes
): ICommonState => {
    switch (action.type) {
        case actions.SET_PROPS:
            return {
                ...state,
                isDoneSetProps: true,
                ...action.payload
            }
        case actions.CLEAR_TYPE_CODE:
            return {
                ...state,
                typeCode: ''
            }
        default:
            return state
    }
}

const rootReducer = combineReducers({
    commonReducer,
    supportTicketReducer
})

export default rootReducer
